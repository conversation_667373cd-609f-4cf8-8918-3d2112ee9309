/* Custom CSS for Event Booking System */

/* Global Styles */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Map container styles */
.map-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem;
}

.map-overlay {
    background: rgba(248, 249, 250, 0.95);
    backdrop-filter: blur(5px);
}

/* QR Code styles */
.qr-code-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.qr-code-container:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

/* Ticket styles */
.ticket-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #007bff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.ticket-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3, #007bff);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.ticket-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    position: relative;
}

.ticket-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #0056b3;
}

/* Print styles for tickets */
@media print {
    .no-print {
        display: none !important;
    }

    .ticket-container {
        border: 3px solid #000 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .ticket-header {
        background: #000 !important;
        color: white !important;
    }

    body {
        background: white !important;
    }

    .card {
        border: 2px solid #000 !important;
        box-shadow: none !important;
    }
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.event-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.event-card .card-img-top {
    transition: transform 0.3s ease;
}

.event-card:hover .card-img-top {
    transform: scale(1.05);
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #1e7e34);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(45deg, #1e7e34, #155724);
    transform: translateY(-1px);
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Cart Badge */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Progress Bars */
.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-primary {
    border-left-color: var(--primary-color);
}

.alert-success {
    border-left-color: var(--success-color);
}

.alert-danger {
    border-left-color: var(--danger-color);
}

.alert-warning {
    border-left-color: var(--warning-color);
}

.alert-info {
    border-left-color: var(--info-color);
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 40px 0 20px;
    margin-top: 50px;
}

.footer h5 {
    color: white;
    margin-bottom: 1rem;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: white;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .hero-section h1 {
        font-size: 1.75rem;
    }
    
    .hero-section .lead {
        font-size: 1rem;
    }
    
    .btn {
        padding: 0.375rem 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* Print Styles */
@media print {
    .btn, .alert, nav, footer, .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .card-header {
        background: #f8f9fa !important;
        color: #000 !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
}

/* Custom Utilities */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.border-radius-lg {
    border-radius: 15px !important;
}

.sticky-top-custom {
    position: sticky;
    top: 20px;
    z-index: 1020;
}
