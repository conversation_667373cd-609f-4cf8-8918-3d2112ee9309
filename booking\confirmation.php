<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Booking Confirmation';

// Require login
requireLogin();

// Get booking reference from URL
$bookingReference = isset($_GET['ref']) ? sanitizeInput($_GET['ref']) : '';

if (empty($bookingReference)) {
    setFlashMessage('danger', 'Invalid booking reference.');
    redirect('../index.php');
}

// Get booking details
$booking = $bookingManager->getBookingByReference($bookingReference);

if (!$booking) {
    setFlashMessage('danger', 'Booking not found.');
    redirect('../index.php');
}

// Check if booking belongs to current user
if ($booking->user_id != $_SESSION['user_id']) {
    setFlashMessage('danger', 'Access denied.');
    redirect('../index.php');
}
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Success Header -->
    <div class="row mb-4">
        <div class="col-12 text-center">
            <div class="mb-4">
                <i class="fas fa-check-circle text-success" style="font-size: 64px;"></i>
            </div>
            <h1 class="text-success">Booking Confirmed!</h1>
            <p class="lead text-muted">Thank you for your booking. Your tickets have been confirmed.</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Booking Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>Booking Details</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary"><?php echo htmlspecialchars($booking->title); ?></h5>
                            <p class="mb-2">
                                <i class="fas fa-calendar text-muted me-2"></i>
                                <strong>Date:</strong> <?php echo formatDate($booking->event_date); ?>
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-clock text-muted me-2"></i>
                                <strong>Time:</strong> <?php echo formatTime($booking->event_time); ?>
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                <strong>Venue:</strong> <?php echo htmlspecialchars($booking->venue . ', ' . $booking->location); ?>
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-user text-muted me-2"></i>
                                <strong>Organizer:</strong> <?php echo htmlspecialchars($booking->organizer); ?>
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-envelope text-muted me-2"></i>
                                <strong>Contact:</strong> <?php echo htmlspecialchars($booking->organizer_contact); ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="bg-light p-3 rounded">
                                <h6 class="mb-3">Booking Information</h6>
                                <p class="mb-2">
                                    <strong>Booking Reference:</strong><br>
                                    <span class="badge bg-primary fs-6"><?php echo $booking->booking_reference; ?></span>
                                </p>
                                <p class="mb-2">
                                    <strong>Number of Tickets:</strong> <?php echo $booking->quantity; ?>
                                </p>
                                <p class="mb-2">
                                    <strong>Total Amount:</strong> <?php echo formatCurrency($booking->total_amount); ?>
                                </p>
                                <p class="mb-2">
                                    <strong>Booking Status:</strong> 
                                    <span class="badge bg-success"><?php echo ucfirst($booking->booking_status); ?></span>
                                </p>
                                <p class="mb-0">
                                    <strong>Payment Status:</strong> 
                                    <span class="badge bg-success"><?php echo ucfirst($booking->payment_status); ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendee Information -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-check me-2"></i>Attendee Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>Name:</strong> <?php echo htmlspecialchars($booking->attendee_name); ?>
                            </p>
                            <p class="mb-2">
                                <strong>Email:</strong> <?php echo htmlspecialchars($booking->attendee_email); ?>
                            </p>
                            <?php if (!empty($booking->attendee_phone)): ?>
                            <p class="mb-2">
                                <strong>Phone:</strong> <?php echo htmlspecialchars($booking->attendee_phone); ?>
                            </p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <?php if (!empty($booking->special_requirements)): ?>
                            <p class="mb-0">
                                <strong>Special Requirements:</strong><br>
                                <?php echo nl2br(htmlspecialchars($booking->special_requirements)); ?>
                            </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code Placeholder -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-qrcode me-2"></i>Digital Ticket</h5>
                </div>
                <div class="card-body text-center">
                    <div class="bg-light p-4 rounded d-inline-block">
                        <i class="fas fa-qrcode text-muted" style="font-size: 100px;"></i>
                        <p class="mt-3 mb-0 text-muted">
                            <strong>QR Code Ticket</strong><br>
                            Show this code at the event entrance
                        </p>
                    </div>
                    <p class="mt-3 text-muted">
                        <small>QR code generation would be implemented here in a production system</small>
                    </p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>Print Ticket
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-primary w-100" onclick="downloadTicket()">
                                <i class="fas fa-download me-2"></i>Download PDF
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="shareBooking()">
                                <i class="fas fa-share me-2"></i>Share
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="../user/dashboard.php" class="btn btn-outline-success w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>My Bookings
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Important Information -->
            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-info-circle me-2"></i>Important Information</h6>
                <ul class="mb-0">
                    <li>Please arrive at least 30 minutes before the event starts</li>
                    <li>Bring a valid ID that matches the attendee name</li>
                    <li>Show your digital ticket (QR code) or printed ticket at the entrance</li>
                    <li>Contact the organizer if you have any questions about the event</li>
                    <li>Tickets are non-refundable unless the event is cancelled</li>
                </ul>
            </div>

            <!-- Email Confirmation Notice -->
            <div class="alert alert-success">
                <i class="fas fa-envelope-check me-2"></i>
                A confirmation email has been sent to <strong><?php echo htmlspecialchars($booking->attendee_email); ?></strong>
            </div>
        </div>
    </div>
</div>

<script>
function downloadTicket() {
    // In a real implementation, this would generate and download a PDF ticket
    showAlert('info', 'PDF download functionality would be implemented here');
}

function shareBooking() {
    const bookingUrl = window.location.href;
    const text = `I just booked tickets for ${<?php echo json_encode($booking->title); ?>}! Check it out.`;
    
    if (navigator.share) {
        navigator.share({
            title: 'Event Booking Confirmation',
            text: text,
            url: bookingUrl
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(bookingUrl).then(function() {
            showAlert('success', 'Booking link copied to clipboard!');
        });
    }
}

// Print styles
const printStyles = `
    <style>
        @media print {
            .btn, .alert, nav, footer { display: none !important; }
            .card { border: 1px solid #000 !important; box-shadow: none !important; }
            .card-header { background: #f8f9fa !important; color: #000 !important; }
        }
    </style>
`;
document.head.insertAdjacentHTML('beforeend', printStyles);
</script>

<?php include '../includes/footer.php'; ?>
