<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Dashboard';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];

// Get user bookings
$bookings = $bookingManager->getUserBookings($userId);

// Separate upcoming and past events
$upcomingBookings = [];
$pastBookings = [];
$today = date('Y-m-d');

foreach ($bookings as $booking) {
    if ($booking->event_date >= $today) {
        $upcomingBookings[] = $booking;
    } else {
        $pastBookings[] = $booking;
    }
}

// Get user details
$user = $userManager->getUserById($userId);

// Handle booking cancellation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'cancel_booking') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        setFlashMessage('danger', 'Invalid security token.');
    } else {
        $bookingId = (int)$_POST['booking_id'];
        
        // Verify booking belongs to user
        $bookingToCancel = null;
        foreach ($bookings as $booking) {
            if ($booking->id == $bookingId) {
                $bookingToCancel = $booking;
                break;
            }
        }
        
        if ($bookingToCancel && $bookingToCancel->event_date >= $today) {
            if ($bookingManager->cancelBooking($bookingId)) {
                setFlashMessage('success', 'Booking cancelled successfully.');
            } else {
                setFlashMessage('danger', 'Failed to cancel booking.');
            }
        } else {
            setFlashMessage('danger', 'Cannot cancel this booking.');
        }
        
        redirect('dashboard.php');
    }
}
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h1>
            <p class="text-muted">Welcome back, <?php echo htmlspecialchars($user->first_name); ?>!</p>
        </div>
    </div>

    <!-- Dashboard Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($upcomingBookings); ?></h4>
                            <p class="mb-0">Upcoming Events</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($pastBookings); ?></h4>
                            <p class="mb-0">Past Events</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($bookings); ?></h4>
                            <p class="mb-0">Total Bookings</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ticket-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $cartManager->getCartCount($userId); ?></h4>
                            <p class="mb-0">Cart Items</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="../events/search.php" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>Browse Events
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="../booking/cart.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-shopping-cart me-2"></i>View Cart
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="profile.php" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-user-edit me-2"></i>Edit Profile
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-info w-100" onclick="downloadAllTickets()">
                                <i class="fas fa-download me-2"></i>Download Tickets
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Events -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-plus me-2"></i>Upcoming Events</h5>
                    <span class="badge bg-primary"><?php echo count($upcomingBookings); ?></span>
                </div>
                <div class="card-body">
                    <?php if (!empty($upcomingBookings)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Date & Time</th>
                                        <th>Venue</th>
                                        <th>Tickets</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($upcomingBookings as $booking): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo $booking->image_url ?: '../assets/images/default-event.jpg'; ?>" 
                                                     class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;" 
                                                     alt="<?php echo htmlspecialchars($booking->title); ?>">
                                                <div>
                                                    <h6 class="mb-0"><?php echo htmlspecialchars($booking->title); ?></h6>
                                                    <small class="text-muted">Ref: <?php echo $booking->booking_reference; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <?php echo formatDate($booking->event_date); ?><br>
                                                <small class="text-muted"><?php echo formatTime($booking->event_time); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <?php echo htmlspecialchars($booking->venue); ?><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($booking->location); ?></small>
                                            </div>
                                        </td>
                                        <td><?php echo $booking->quantity; ?></td>
                                        <td><?php echo formatCurrency($booking->total_amount); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $booking->booking_status === 'confirmed' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($booking->booking_status); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="../booking/confirmation.php?ref=<?php echo $booking->booking_reference; ?>"
                                                   class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="../booking/download_ticket.php?ref=<?php echo $booking->booking_reference; ?>&action=download"
                                                   class="btn btn-outline-success" title="Download PDF Ticket">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <a href="../booking/download_ticket.php?ref=<?php echo $booking->booking_reference; ?>&action=qr"
                                                   class="btn btn-outline-info" title="Download QR Code">
                                                    <i class="fas fa-qrcode"></i>
                                                </a>
                                                <?php if ($booking->booking_status !== 'cancelled'): ?>
                                                <button class="btn btn-outline-danger" onclick="cancelBooking(<?php echo $booking->id; ?>)" title="Cancel Booking">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times text-muted" style="font-size: 48px;"></i>
                            <h5 class="mt-3 text-muted">No Upcoming Events</h5>
                            <p class="text-muted">You don't have any upcoming event bookings.</p>
                            <a href="../events/search.php" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Browse Events
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Past Events -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>Past Events</h5>
                    <span class="badge bg-secondary"><?php echo count($pastBookings); ?></span>
                </div>
                <div class="card-body">
                    <?php if (!empty($pastBookings)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Date</th>
                                        <th>Venue</th>
                                        <th>Tickets</th>
                                        <th>Amount</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($pastBookings, 0, 5) as $booking): ?>
                                    <tr class="opacity-75">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo $booking->image_url ?: '../assets/images/default-event.jpg'; ?>" 
                                                     class="rounded me-3" style="width: 40px; height: 40px; object-fit: cover;" 
                                                     alt="<?php echo htmlspecialchars($booking->title); ?>">
                                                <div>
                                                    <h6 class="mb-0"><?php echo htmlspecialchars($booking->title); ?></h6>
                                                    <small class="text-muted">Ref: <?php echo $booking->booking_reference; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo formatDate($booking->event_date); ?></td>
                                        <td><?php echo htmlspecialchars($booking->venue); ?></td>
                                        <td><?php echo $booking->quantity; ?></td>
                                        <td><?php echo formatCurrency($booking->total_amount); ?></td>
                                        <td>
                                            <a href="../booking/confirmation.php?ref=<?php echo $booking->booking_reference; ?>" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (count($pastBookings) > 5): ?>
                        <div class="text-center">
                            <button class="btn btn-outline-secondary" onclick="showAllPastEvents()">
                                <i class="fas fa-eye me-2"></i>Show All Past Events (<?php echo count($pastBookings); ?>)
                            </button>
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-history text-muted" style="font-size: 48px;"></i>
                            <h5 class="mt-3 text-muted">No Past Events</h5>
                            <p class="text-muted">Your past event bookings will appear here.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Booking Modal -->
<div class="modal fade" id="cancelBookingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Booking</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this booking? This action cannot be undone.</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Cancellation policies may apply. Please check the event terms and conditions.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Booking</button>
                <form method="POST" class="d-inline" id="cancelBookingForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="cancel_booking">
                    <input type="hidden" name="booking_id" id="cancelBookingId">
                    <button type="submit" class="btn btn-danger">Cancel Booking</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function cancelBooking(bookingId) {
    document.getElementById('cancelBookingId').value = bookingId;
    new bootstrap.Modal(document.getElementById('cancelBookingModal')).show();
}

function printTicket(bookingReference) {
    window.open('../booking/confirmation.php?ref=' + bookingReference, '_blank');
}

function downloadAllTickets() {
    const upcomingBookings = <?php echo json_encode(array_map(function($booking) {
        return $booking->booking_reference;
    }, $upcomingBookings)); ?>;

    if (upcomingBookings.length === 0) {
        showAlert('info', 'No upcoming events to download tickets for.');
        return;
    }

    showAlert('info', 'Downloading tickets... Please wait.');

    // Download each ticket with a small delay to avoid overwhelming the server
    upcomingBookings.forEach((ref, index) => {
        setTimeout(() => {
            const link = document.createElement('a');
            link.href = '../booking/download_ticket.php?ref=' + ref + '&action=download';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }, index * 1000); // 1 second delay between downloads
    });
}

function showAllPastEvents() {
    // In a real implementation, this would load more events via AJAX or redirect to a full history page
    showAlert('info', 'Full event history page would be implemented here');
}
</script>

<?php include '../includes/footer.php'; ?>
