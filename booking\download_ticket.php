<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/TicketGenerator.php';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$bookingRef = isset($_GET['ref']) ? sanitizeInput($_GET['ref']) : '';
$action = isset($_GET['action']) ? sanitizeInput($_GET['action']) : 'download';

if (!$bookingRef) {
    setFlashMessage('danger', 'Invalid booking reference.');
    redirect('../user/dashboard.php');
}

// Verify booking belongs to user
$booking = $bookingManager->getBookingByReference($bookingRef);

if (!$booking) {
    setFlashMessage('danger', 'Booking not found.');
    redirect('../user/dashboard.php');
}

// Check if booking belongs to current user
$userBookings = $bookingManager->getUserBookings($userId);
$isUserBooking = false;
foreach ($userBookings as $userBooking) {
    if ($userBooking->booking_reference === $bookingRef) {
        $isUserBooking = true;
        break;
    }
}

if (!$isUserBooking) {
    setFlashMessage('danger', 'Access denied. This booking does not belong to you.');
    redirect('../user/dashboard.php');
}

// Initialize ticket generator
$ticketGenerator = new TicketGenerator($db);

try {
    if ($action === 'download') {
        // Download PDF ticket
        $filename = 'ticket_' . $booking->title . '_' . $bookingRef . '.pdf';
        $filename = preg_replace('/[^a-zA-Z0-9_\-\.]/', '_', $filename); // Sanitize filename
        
        if ($ticketGenerator->downloadTicket($bookingRef, $filename)) {
            exit; // File downloaded successfully
        } else {
            throw new Exception('Failed to generate PDF ticket');
        }
    } elseif ($action === 'qr') {
        // Generate and display QR code
        $qrCodeDataUri = $ticketGenerator->generateQRCode($bookingRef, 400);

        if ($qrCodeDataUri) {
            // Check if it's SVG or PNG data
            if (strpos($qrCodeDataUri, 'data:image/svg+xml') === 0) {
                // SVG QR code
                $qrCodeData = explode(',', $qrCodeDataUri)[1];
                $qrCodeContent = base64_decode($qrCodeData);

                header('Content-Type: image/svg+xml');
                header('Content-Disposition: attachment; filename="qr_code_' . $bookingRef . '.svg"');
                header('Content-Length: ' . strlen($qrCodeContent));

                echo $qrCodeContent;
            } else {
                // PNG QR code
                $qrCodeData = explode(',', $qrCodeDataUri)[1];
                $qrCodeBinary = base64_decode($qrCodeData);

                header('Content-Type: image/png');
                header('Content-Disposition: attachment; filename="qr_code_' . $bookingRef . '.png"');
                header('Content-Length: ' . strlen($qrCodeBinary));

                echo $qrCodeBinary;
            }
            exit;
        } else {
            throw new Exception('Failed to generate QR code');
        }
    } else {
        throw new Exception('Invalid action specified');
    }
    
} catch (Exception $e) {
    error_log("Ticket download error: " . $e->getMessage());
    setFlashMessage('danger', 'Failed to generate ticket. Please try again later.');
    redirect('../user/dashboard.php');
}
?>
