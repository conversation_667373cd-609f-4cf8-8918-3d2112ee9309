<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Register';
$error = '';
$success = '';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('../index.php');
}

// Process registration form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $username = sanitizeInput($_POST['username']);
        $email = sanitizeInput($_POST['email']);
        $password = $_POST['password'];
        $confirmPassword = $_POST['confirm_password'];
        $firstName = sanitizeInput($_POST['first_name']);
        $lastName = sanitizeInput($_POST['last_name']);
        $phone = sanitizeInput($_POST['phone']);
        $address = sanitizeInput($_POST['address']);

        // Validation
        if (empty($username) || empty($email) || empty($password) || empty($firstName) || empty($lastName)) {
            $error = 'Please fill in all required fields.';
        } elseif (strlen($username) < 3) {
            $error = 'Username must be at least 3 characters long.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } elseif (strlen($password) < 6) {
            $error = 'Password must be at least 6 characters long.';
        } elseif ($password !== $confirmPassword) {
            $error = 'Passwords do not match.';
        } else {
            // Attempt registration
            if ($userManager->register($username, $email, $password, $firstName, $lastName, $phone, $address)) {
                $success = 'Registration successful! You can now log in.';
                // Clear form data
                $_POST = array();
            } else {
                $error = 'Username or email already exists. Please choose different ones.';
            }
        }
    }
}
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus text-primary" style="font-size: 48px;"></i>
                        <h2 class="mt-3">Create Account</h2>
                        <p class="text-muted">Join us to start booking amazing events</p>
                    </div>

                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                            <div class="mt-2">
                                <a href="login.php" class="btn btn-success btn-sm">
                                    <i class="fas fa-sign-in-alt me-1"></i>Login Now
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <form method="POST" id="registerForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name"
                                       data-field-name="First Name"
                                       value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>"
                                       required minlength="2">
                                <div class="form-text">At least 2 characters</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name"
                                       data-field-name="Last Name"
                                       value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>"
                                       required minlength="2">
                                <div class="form-text">At least 2 characters</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="username" name="username"
                                       data-field-name="Username"
                                       value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                                       required minlength="3" maxlength="50"
                                       pattern="[a-zA-Z0-9_]+"
                                       data-pattern-message="Username can only contain letters, numbers, and underscores">
                            </div>
                            <div class="form-text">3-50 characters, letters, numbers, and underscores only</div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email" name="email"
                                       data-field-name="Email Address"
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                                       required maxlength="255">
                            </div>
                            <div class="form-text">We'll never share your email with anyone else</div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       data-field-name="Phone Number"
                                       placeholder="+1234567890"
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                            <div class="form-text">Optional - Include country code for international numbers</div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password"
                                           data-field-name="Password"
                                           required minlength="6" maxlength="255">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password', 'passwordToggle1')">
                                        <i class="fas fa-eye" id="passwordToggle1"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <div id="password-strength" class="mt-1"></div>
                                    At least 6 characters - Use a mix of letters, numbers, and symbols
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                           data-field-name="Confirm Password"
                                           required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password', 'passwordToggle2')">
                                        <i class="fas fa-eye" id="passwordToggle2"></i>
                                    </button>
                                </div>
                                <div class="form-text">Re-enter your password to confirm</div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and
                                <a href="#" class="text-decoration-none">Privacy Policy</a> *
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="text-muted">
                            Already have an account?
                            <a href="login.php" class="text-decoration-none">Sign in here</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId, toggleId) {
    const passwordField = document.getElementById(fieldId);
    const passwordToggle = document.getElementById(toggleId);

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        passwordToggle.classList.remove('fa-eye');
        passwordToggle.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        passwordToggle.classList.remove('fa-eye-slash');
        passwordToggle.classList.add('fa-eye');
    }
}

// Setup real-time validation
document.addEventListener('DOMContentLoaded', function() {
    setupRealTimeValidation('registerForm');

    // Password strength indicator
    const passwordField = document.getElementById('password');
    const strengthIndicator = document.getElementById('password-strength');

    passwordField.addEventListener('input', function() {
        const password = this.value;
        const strength = checkPasswordStrength(password);
        updatePasswordStrength(strengthIndicator, strength);
    });
});

// Enhanced form validation
document.getElementById('registerForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const terms = document.getElementById('terms').checked;

    if (!validateForm('registerForm')) {
        e.preventDefault();
        return;
    }

    if (!terms) {
        e.preventDefault();
        showAlert('danger', 'Please accept the Terms of Service and Privacy Policy.');
        document.getElementById('terms').focus();
        return;
    }

    if (password !== confirmPassword) {
        e.preventDefault();
        showAlert('danger', 'Passwords do not match.');
        document.getElementById('confirm_password').focus();
        return;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
    submitBtn.disabled = true;

    // Re-enable button after 10 seconds as fallback
    setTimeout(function() {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

// Password strength checker
function checkPasswordStrength(password) {
    let score = 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[^A-Za-z0-9]/.test(password)
    };

    score = Object.values(checks).filter(Boolean).length;

    if (password.length < 6) return { score: 0, text: 'Too short', class: 'text-danger' };
    if (score < 2) return { score: 1, text: 'Weak', class: 'text-danger' };
    if (score < 4) return { score: 2, text: 'Fair', class: 'text-warning' };
    if (score < 5) return { score: 3, text: 'Good', class: 'text-info' };
    return { score: 4, text: 'Strong', class: 'text-success' };
}

// Update password strength indicator
function updatePasswordStrength(indicator, strength) {
    if (!strength || strength.score === 0) {
        indicator.innerHTML = '';
        return;
    }

    const bars = Array(4).fill(0).map((_, i) =>
        `<div class="strength-bar ${i < strength.score ? 'active' : ''}" style="width: 20px; height: 4px; display: inline-block; margin-right: 2px; background-color: ${i < strength.score ? (strength.class === 'text-danger' ? '#dc3545' : strength.class === 'text-warning' ? '#ffc107' : strength.class === 'text-info' ? '#17a2b8' : '#28a745') : '#e9ecef'};"></div>`
    ).join('');

    indicator.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-2">${bars}</div>
            <small class="${strength.class}">${strength.text}</small>
        </div>
    `;
}
</script>

<?php include '../includes/footer.php'; ?>
