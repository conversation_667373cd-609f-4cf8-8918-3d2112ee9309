<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Ticket Verification';

// Get booking reference from URL
$bookingRef = isset($_GET['ref']) ? sanitizeInput($_GET['ref']) : '';

if (!$bookingRef) {
    setFlashMessage('danger', 'Invalid ticket reference.');
    redirect('../index.php');
}

// Get booking details
$booking = $bookingManager->getBookingByReference($bookingRef);

if (!$booking) {
    $isValid = false;
    $message = 'Ticket not found or invalid.';
} else {
    $isValid = true;
    $message = 'Valid ticket found.';
    
    // Check if event has passed
    $eventDateTime = $booking->event_date . ' ' . $booking->event_time;
    $isPastEvent = strtotime($eventDateTime) < time();
}
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Verification Result -->
            <div class="card shadow mb-4">
                <div class="card-header text-center <?php echo $isValid ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h3 class="mb-0">
                        <i class="fas <?php echo $isValid ? 'fa-check-circle' : 'fa-times-circle'; ?> me-2"></i>
                        Ticket Verification
                    </h3>
                </div>
                <div class="card-body text-center">
                    <?php if ($isValid): ?>
                        <div class="alert alert-success">
                            <h4><i class="fas fa-check-circle me-2"></i>Valid Ticket</h4>
                            <p class="mb-0">This ticket is authentic and valid for entry.</p>
                        </div>
                        
                        <!-- Event Details -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($booking->title); ?></h5>
                                        <div class="text-start">
                                            <p class="mb-2"><strong>Attendee:</strong> <?php echo htmlspecialchars($booking->attendee_name); ?></p>
                                            <p class="mb-2"><strong>Date:</strong> <?php echo formatDate($booking->event_date); ?></p>
                                            <p class="mb-2"><strong>Time:</strong> <?php echo formatTime($booking->event_time); ?></p>
                                            <p class="mb-2"><strong>Venue:</strong> <?php echo htmlspecialchars($booking->venue); ?></p>
                                            <p class="mb-2"><strong>Tickets:</strong> <?php echo $booking->quantity; ?></p>
                                            <p class="mb-0"><strong>Reference:</strong> <?php echo htmlspecialchars($booking->booking_reference); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title">Status</h5>
                                        <div class="text-start">
                                            <p class="mb-2">
                                                <strong>Booking Status:</strong> 
                                                <span class="badge bg-<?php echo $booking->booking_status === 'confirmed' ? 'success' : 'warning'; ?>">
                                                    <?php echo ucfirst($booking->booking_status); ?>
                                                </span>
                                            </p>
                                            <p class="mb-2">
                                                <strong>Payment Status:</strong> 
                                                <span class="badge bg-<?php echo $booking->payment_status === 'completed' ? 'success' : 'warning'; ?>">
                                                    <?php echo ucfirst($booking->payment_status); ?>
                                                </span>
                                            </p>
                                            <p class="mb-2">
                                                <strong>Event Status:</strong> 
                                                <span class="badge bg-<?php echo $isPastEvent ? 'secondary' : 'primary'; ?>">
                                                    <?php echo $isPastEvent ? 'Past Event' : 'Upcoming'; ?>
                                                </span>
                                            </p>
                                            <p class="mb-0">
                                                <strong>Verified:</strong> 
                                                <span class="text-success">
                                                    <i class="fas fa-check me-1"></i><?php echo date('M j, Y g:i A'); ?>
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($isPastEvent): ?>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            This event has already taken place.
                        </div>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <h4><i class="fas fa-times-circle me-2"></i>Invalid Ticket</h4>
                            <p class="mb-0">This ticket reference is not valid or does not exist in our system.</p>
                        </div>
                        
                        <div class="mt-4">
                            <p class="text-muted">If you believe this is an error, please contact the event organizer or our support team.</p>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Action Buttons -->
                    <div class="mt-4">
                        <?php if ($isValid): ?>
                            <a href="../events/details.php?id=<?php echo $booking->event_id; ?>" class="btn btn-primary me-2">
                                <i class="fas fa-eye me-2"></i>View Event Details
                            </a>
                            <a href="../booking/confirmation.php?ref=<?php echo $booking->booking_reference; ?>" class="btn btn-outline-primary me-2">
                                <i class="fas fa-ticket-alt me-2"></i>View Full Ticket
                            </a>
                        <?php endif; ?>
                        <a href="../index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Security Notice -->
            <div class="card shadow">
                <div class="card-body">
                    <h6><i class="fas fa-shield-alt text-primary me-2"></i>Security Notice</h6>
                    <p class="text-muted mb-0">
                        This verification system ensures the authenticity of tickets. Each QR code is unique and linked to a specific booking. 
                        If you suspect fraudulent activity, please report it immediately.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh verification status every 30 seconds for active events
<?php if ($isValid && !$isPastEvent): ?>
setTimeout(function() {
    location.reload();
}, 30000);
<?php endif; ?>
</script>

<?php include '../includes/footer.php'; ?>
