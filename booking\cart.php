<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Shopping Cart';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];

// Handle cart updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        setFlashMessage('danger', 'Invalid security token.');
    } else {
        $action = $_POST['action'];
        $eventId = (int)$_POST['event_id'];

        switch ($action) {
            case 'update':
                $quantity = (int)$_POST['quantity'];
                if ($cartManager->updateCartItem($userId, $eventId, $quantity)) {
                    setFlashMessage('success', 'Cart updated successfully.');
                } else {
                    setFlashMessage('danger', 'Failed to update cart.');
                }
                break;

            case 'remove':
                if ($cartManager->removeFromCart($userId, $eventId)) {
                    setFlashMessage('success', 'Item removed from cart.');
                } else {
                    setFlashMessage('danger', 'Failed to remove item.');
                }
                break;

            case 'clear':
                if ($cartManager->clearCart($userId)) {
                    setFlashMessage('success', 'Cart cleared successfully.');
                } else {
                    setFlashMessage('danger', 'Failed to clear cart.');
                }
                break;
        }

        // Redirect to prevent form resubmission
        redirect('cart.php');
    }
}

// Get cart items
$cartItems = $cartManager->getCartItems($userId);
$cartTotal = $cartManager->getCartTotal($userId);
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                    <li class="breadcrumb-item active">Shopping Cart</li>
                </ol>
            </nav>
            <h1><i class="fas fa-shopping-cart me-2"></i>Shopping Cart</h1>
        </div>
    </div>

    <?php if (!empty($cartItems)): ?>
    <div class="row">
        <!-- Cart Items -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Cart Items (<?php echo count($cartItems); ?>)</h5>
                    <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to clear your cart?')">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="clear">
                        <button type="submit" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash"></i> Clear Cart
                        </button>
                    </form>
                </div>
                <div class="card-body p-0">
                    <?php foreach ($cartItems as $item): ?>
                    <div class="border-bottom p-4">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <img src="<?php echo $item->image_url ?: '../assets/images/default-event.jpg'; ?>"
                                     class="img-fluid rounded" alt="<?php echo htmlspecialchars($item->title); ?>"
                                     style="height: 80px; object-fit: cover;">
                            </div>
                            <div class="col-md-4">
                                <h6 class="mb-1"><?php echo htmlspecialchars($item->title); ?></h6>
                                <p class="text-muted small mb-1">
                                    <i class="fas fa-calendar me-1"></i><?php echo formatDate($item->event_date); ?> at <?php echo formatTime($item->event_time); ?>
                                </p>
                                <p class="text-muted small mb-0">
                                    <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($item->venue); ?>
                                </p>
                            </div>
                            <div class="col-md-2">
                                <strong><?php echo formatCurrency($item->price); ?></strong>
                                <small class="text-muted d-block">per ticket</small>
                            </div>
                            <div class="col-md-2">
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="action" value="update">
                                    <input type="hidden" name="event_id" value="<?php echo $item->event_id; ?>">
                                    <div class="input-group input-group-sm">
                                        <button type="button" class="btn btn-outline-secondary" onclick="decreaseQuantity(this)">-</button>
                                        <input type="number" class="form-control text-center" name="quantity"
                                               value="<?php echo $item->quantity; ?>" min="1" max="<?php echo $item->available_tickets; ?>"
                                               onchange="this.form.submit()">
                                        <button type="button" class="btn btn-outline-secondary" onclick="increaseQuantity(this)">+</button>
                                    </div>
                                </form>
                                <small class="text-muted">Max: <?php echo $item->available_tickets; ?></small>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="mb-2">
                                    <strong><?php echo formatCurrency($item->price * $item->quantity); ?></strong>
                                </div>
                                <form method="POST" class="d-inline" onsubmit="return confirm('Remove this item from cart?')">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="action" value="remove">
                                    <input type="hidden" name="event_id" value="<?php echo $item->event_id; ?>">
                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Cart Summary -->
        <div class="col-lg-4">
            <div class="card shadow sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <strong><?php echo formatCurrency($cartTotal); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Service Fee:</span>
                        <span><?php echo formatCurrency(0); ?></span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <strong>Total:</strong>
                        <strong class="text-primary"><?php echo formatCurrency($cartTotal); ?></strong>
                    </div>

                    <a href="checkout.php" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                    </a>

                    <a href="../events/search.php" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                    </a>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <i class="fas fa-shield-alt text-success mb-2" style="font-size: 24px;"></i>
                    <h6>Secure Checkout</h6>
                    <small class="text-muted">Your payment information is protected with industry-standard encryption.</small>
                </div>
            </div>
        </div>
    </div>

    <?php else: ?>
    <!-- Empty Cart -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart text-muted" style="font-size: 64px;"></i>
                <h3 class="mt-3 text-muted">Your Cart is Empty</h3>
                <p class="text-muted">Looks like you haven't added any events to your cart yet.</p>
                <a href="../events/search.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>Browse Events
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function increaseQuantity(button) {
    const input = button.previousElementSibling;
    const max = parseInt(input.getAttribute('max'));
    const current = parseInt(input.value);

    if (current < max) {
        input.value = current + 1;
        input.form.submit();
    }
}

function decreaseQuantity(button) {
    const input = button.nextElementSibling;
    const current = parseInt(input.value);

    if (current > 1) {
        input.value = current - 1;
        input.form.submit();
    }
}

function removeItem(eventId, eventTitle) {
    confirmAction(
        'Remove Item',
        `Are you sure you want to remove "${eventTitle}" from your cart?`,
        'Remove',
        'btn-danger',
        function() {
            showLoading();
            document.getElementById('removeForm' + eventId).submit();
        }
    );
}

function clearCart() {
    confirmAction(
        'Clear Cart',
        'Are you sure you want to clear your entire cart? This action cannot be undone.',
        'Clear Cart',
        'btn-danger',
        function() {
            showLoading();
            document.getElementById('clearCartForm').submit();
        }
    );
}

// Enhanced quantity validation and auto-submit
document.addEventListener('DOMContentLoaded', function() {
    const quantityInputs = document.querySelectorAll('input[name="quantity"]');
    let quantityTimeout;

    quantityInputs.forEach(input => {
        // Real-time validation
        input.addEventListener('input', function() {
            const min = parseInt(this.getAttribute('min')) || 1;
            const max = parseInt(this.getAttribute('max')) || 999;
            let value = parseInt(this.value);

            if (isNaN(value) || value < min) {
                this.value = min;
                showAlert('warning', `Minimum quantity is ${min}.`);
            } else if (value > max) {
                this.value = max;
                showAlert('warning', `Maximum available tickets is ${max}.`);
            }

            // Auto-submit after delay
            clearTimeout(quantityTimeout);
            quantityTimeout = setTimeout(() => {
                showLoading();
                this.form.submit();
            }, 1500);
        });

        // Prevent non-numeric input
        input.addEventListener('keypress', function(e) {
            if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                e.preventDefault();
            }
        });

        // Validate on blur
        input.addEventListener('blur', function() {
            const min = parseInt(this.getAttribute('min')) || 1;
            const max = parseInt(this.getAttribute('max')) || 999;
            let value = parseInt(this.value);

            if (isNaN(value) || value < min) {
                this.value = min;
                this.form.submit();
            } else if (value > max) {
                this.value = max;
                this.form.submit();
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
