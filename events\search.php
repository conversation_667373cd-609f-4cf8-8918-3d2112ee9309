<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Browse Events';

// Get search parameters
$searchTerm = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$location = isset($_GET['location']) ? sanitizeInput($_GET['location']) : '';
$date = isset($_GET['date']) ? sanitizeInput($_GET['date']) : '';
$category = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = EVENTS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get events based on search criteria
if (!empty($searchTerm) || !empty($location) || !empty($date)) {
    $events = $eventManager->searchEvents($searchTerm, $location, $date);
} elseif (!empty($category)) {
    $events = $eventManager->getEventsByCategory($category);
} else {
    $events = $eventManager->getAllEvents();
}

// Filter by category if specified
if (!empty($category) && (empty($searchTerm) && empty($location) && empty($date))) {
    $events = array_filter($events, function($event) use ($category) {
        return $event->category === $category;
    });
}

// Get unique categories for filter
$db->query('SELECT DISTINCT category FROM events WHERE status = "active" ORDER BY category');
$categories = $db->resultset();

// Pagination calculation
$totalEvents = count($events);
$totalPages = ceil($totalEvents / $limit);
$events = array_slice($events, $offset, $limit);
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1>Browse Events</h1>
            <p class="text-muted">Discover amazing events happening near you</p>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search Events</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="Event name or keyword" value="<?php echo htmlspecialchars($searchTerm); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   placeholder="City or venue" value="<?php echo htmlspecialchars($location); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date" 
                                   value="<?php echo htmlspecialchars($date); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat->category); ?>" 
                                            <?php echo $category === $cat->category ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat->category); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <?php if (!empty($searchTerm) || !empty($location) || !empty($date) || !empty($category)): ?>
                    <div class="mt-3">
                        <a href="search.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i> Clear Filters
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <?php echo $totalEvents; ?> Event<?php echo $totalEvents !== 1 ? 's' : ''; ?> Found
                    </h5>
                    <?php if (!empty($searchTerm) || !empty($location) || !empty($date) || !empty($category)): ?>
                        <small class="text-muted">
                            Showing results for: 
                            <?php 
                            $filters = [];
                            if (!empty($searchTerm)) $filters[] = "\"$searchTerm\"";
                            if (!empty($location)) $filters[] = "in $location";
                            if (!empty($date)) $filters[] = "on " . formatDate($date);
                            if (!empty($category)) $filters[] = "category: $category";
                            echo implode(', ', $filters);
                            ?>
                        </small>
                    <?php endif; ?>
                </div>
                <div>
                    <select class="form-select form-select-sm" onchange="sortEvents(this.value)">
                        <option value="date_asc">Sort by Date (Earliest)</option>
                        <option value="date_desc">Sort by Date (Latest)</option>
                        <option value="price_asc">Sort by Price (Low to High)</option>
                        <option value="price_desc">Sort by Price (High to Low)</option>
                        <option value="name_asc">Sort by Name (A-Z)</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Events Grid -->
    <?php if (!empty($events)): ?>
    <div class="row" id="eventsGrid">
        <?php foreach ($events as $event): ?>
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card event-card h-100">
                <div class="position-relative">
                    <img src="<?php echo $event->image_url ?: '../assets/images/default-event.jpg'; ?>" 
                         class="card-img-top" alt="<?php echo htmlspecialchars($event->title); ?>" 
                         style="height: 200px; object-fit: cover;">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge bg-primary"><?php echo formatCurrency($event->price); ?></span>
                    </div>
                    <div class="position-absolute top-0 start-0 m-2">
                        <span class="badge bg-secondary"><?php echo htmlspecialchars($event->category); ?></span>
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title"><?php echo htmlspecialchars($event->title); ?></h5>
                    <p class="card-text text-muted small mb-2">
                        <i class="fas fa-calendar me-1"></i><?php echo formatDate($event->event_date); ?> at <?php echo formatTime($event->event_time); ?>
                    </p>
                    <p class="card-text text-muted small mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($event->venue . ', ' . $event->location); ?>
                    </p>
                    <p class="card-text text-muted small mb-2">
                        <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($event->organizer); ?>
                    </p>
                    <p class="card-text flex-grow-1"><?php echo substr(htmlspecialchars($event->description), 0, 100) . '...'; ?></p>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">
                                <i class="fas fa-ticket-alt me-1"></i><?php echo $event->available_tickets; ?> tickets left
                            </small>
                            <div class="progress" style="width: 100px; height: 6px;">
                                <?php 
                                $percentage = ($event->available_tickets / $event->total_tickets) * 100;
                                $progressClass = $percentage > 50 ? 'bg-success' : ($percentage > 20 ? 'bg-warning' : 'bg-danger');
                                ?>
                                <div class="progress-bar <?php echo $progressClass; ?>" style="width: <?php echo $percentage; ?>%"></div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="details.php?id=<?php echo $event->id; ?>" class="btn btn-outline-primary btn-sm flex-grow-1">
                                <i class="fas fa-info-circle"></i> Details
                            </a>
                            <?php if ($event->available_tickets > 0): ?>
                            <button class="btn btn-primary btn-sm flex-grow-1" onclick="addToCart(<?php echo $event->id; ?>)">
                                <i class="fas fa-cart-plus"></i> Add to Cart
                            </button>
                            <?php else: ?>
                            <button class="btn btn-secondary btn-sm flex-grow-1" disabled>
                                <i class="fas fa-times"></i> Sold Out
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
    <div class="row">
        <div class="col-12">
            <nav aria-label="Events pagination">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
    <?php endif; ?>

    <?php else: ?>
    <!-- No Events Found -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search text-muted" style="font-size: 64px;"></i>
                <h3 class="mt-3 text-muted">No Events Found</h3>
                <p class="text-muted">Try adjusting your search criteria or browse all events.</p>
                <a href="search.php" class="btn btn-primary">
                    <i class="fas fa-eye"></i> Browse All Events
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function sortEvents(sortBy) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortBy);
    window.location.href = url.toString();
}

// Auto-submit form on category change
document.getElementById('category').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php include '../includes/footer.php'; ?>
