<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/TicketGenerator.php';

// Test QR code generation
$ticketGenerator = new TicketGenerator($db);

// Test with a sample booking reference
$testBookingRef = 'BK20241201ABC123';
$qrCodeDataUri = $ticketGenerator->generateQRCode($testBookingRef, 300);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">QR Code Generation Test</h5>
                    </div>
                    <div class="card-body text-center">
                        <h6>Test Booking Reference: <?php echo htmlspecialchars($testBookingRef); ?></h6>
                        
                        <?php if ($qrCodeDataUri): ?>
                            <div class="mt-3">
                                <img src="<?php echo $qrCodeDataUri; ?>" alt="QR Code" class="img-fluid" style="max-width: 300px;">
                            </div>
                            <div class="mt-3">
                                <p class="text-success">
                                    <i class="fas fa-check-circle"></i> QR Code generated successfully!
                                </p>
                                <small class="text-muted">
                                    Verification URL: <?php echo SITE_URL; ?>/booking/verify.php?ref=<?php echo $testBookingRef; ?>
                                </small>
                            </div>
                        <?php else: ?>
                            <div class="mt-3">
                                <p class="text-danger">
                                    <i class="fas fa-times-circle"></i> Failed to generate QR Code
                                </p>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-primary">Back to Home</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
