<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Admin Dashboard';

// Require admin access
requireAdmin();

// Get dashboard statistics
$db->query('SELECT COUNT(*) as total FROM events WHERE status = "active"');
$totalEvents = $db->single()->total;

$db->query('SELECT COUNT(*) as total FROM users WHERE role = "user"');
$totalUsers = $db->single()->total;

$db->query('SELECT COUNT(*) as total FROM bookings WHERE booking_status = "confirmed"');
$totalBookings = $db->single()->total;

$db->query('SELECT SUM(total_amount) as total FROM bookings WHERE booking_status = "confirmed"');
$totalRevenue = $db->single()->total ?: 0;

// Get recent bookings
$db->query('SELECT b.*, e.title, u.first_name, u.last_name
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           JOIN users u ON b.user_id = u.id
           ORDER BY b.created_at DESC
           LIMIT 5');
$recentBookings = $db->resultset();

// Get upcoming events
$db->query('SELECT * FROM events WHERE event_date >= CURDATE() AND status = "active" ORDER BY event_date ASC LIMIT 5');
$upcomingEvents = $db->resultset();
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</h1>
            <p class="text-muted">Welcome to the Event Booking System administration panel</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $totalEvents; ?></h3>
                            <p class="mb-0">Active Events</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-primary border-0">
                    <a href="events.php" class="text-white text-decoration-none">
                        <small>View all events <i class="fas fa-arrow-right"></i></small>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $totalUsers; ?></h3>
                            <p class="mb-0">Registered Users</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-success border-0">
                    <a href="#" class="text-white text-decoration-none">
                        <small>Manage users <i class="fas fa-arrow-right"></i></small>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $totalBookings; ?></h3>
                            <p class="mb-0">Total Bookings</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ticket-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-info border-0">
                    <a href="bookings.php" class="text-white text-decoration-none">
                        <small>View bookings <i class="fas fa-arrow-right"></i></small>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo formatCurrency($totalRevenue); ?></h3>
                            <p class="mb-0">Total Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-warning border-0">
                    <a href="reports.php" class="text-white text-decoration-none">
                        <small>View reports <i class="fas fa-arrow-right"></i></small>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="events.php?action=add" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>Add New Event
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="bookings.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-2"></i>View All Bookings
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="reports.php" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-chart-bar me-2"></i>Generate Reports
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="../index.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-eye me-2"></i>View Site
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Bookings -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Bookings</h5>
                    <a href="bookings.php" class="btn btn-outline-primary btn-sm">View All</a>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentBookings)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Booking Ref</th>
                                        <th>Customer</th>
                                        <th>Event</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentBookings as $booking): ?>
                                    <tr>
                                        <td>
                                            <code><?php echo $booking->booking_reference; ?></code>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($booking->first_name . ' ' . $booking->last_name); ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($booking->title); ?>
                                        </td>
                                        <td>
                                            <?php echo formatCurrency($booking->total_amount); ?>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($booking->created_at)); ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $booking->booking_status === 'confirmed' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($booking->booking_status); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox text-muted" style="font-size: 48px;"></i>
                            <h5 class="mt-3 text-muted">No Recent Bookings</h5>
                            <p class="text-muted">Recent booking activity will appear here.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-plus me-2"></i>Upcoming Events</h5>
                    <a href="events.php" class="btn btn-outline-primary btn-sm">Manage</a>
                </div>
                <div class="card-body">
                    <?php if (!empty($upcomingEvents)): ?>
                        <?php foreach ($upcomingEvents as $event): ?>
                        <div class="border-bottom pb-3 mb-3">
                            <h6 class="mb-1"><?php echo htmlspecialchars($event->title); ?></h6>
                            <p class="text-muted small mb-1">
                                <i class="fas fa-calendar me-1"></i><?php echo formatDate($event->event_date); ?>
                            </p>
                            <p class="text-muted small mb-1">
                                <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($event->venue); ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <?php echo $event->available_tickets; ?>/<?php echo $event->total_tickets; ?> tickets left
                                </small>
                                <span class="badge bg-primary"><?php echo formatCurrency($event->price); ?></span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times text-muted" style="font-size: 32px;"></i>
                            <p class="mt-2 mb-0 text-muted">No upcoming events</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- System Status -->
            <div class="card shadow mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-server me-2"></i>System Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Database</span>
                        <span class="badge bg-success">Online</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Payment Gateway</span>
                        <span class="badge bg-warning">Demo Mode</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Email Service</span>
                        <span class="badge bg-success">Active</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Backup Status</span>
                        <span class="badge bg-info">Daily</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
