<?php
require_once 'config.php';

class TicketGenerator {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Generate QR code for a booking using Google Charts API
     */
    public function generateQRCode($bookingReference, $size = 300) {
        try {
            // Create verification URL
            $verificationUrl = SITE_URL . "/booking/verify.php?ref=" . $bookingReference;

            // Use Google Charts API for QR code generation
            $qrUrl = "https://chart.googleapis.com/chart?chs={$size}x{$size}&cht=qr&chl=" . urlencode($verificationUrl);

            // Get the QR code image data
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Event Booking System'
                ]
            ]);

            $qrImageData = @file_get_contents($qrUrl, false, $context);

            if ($qrImageData !== false) {
                return 'data:image/png;base64,' . base64_encode($qrImageData);
            } else {
                // Fallback: return a placeholder data URI
                return $this->generatePlaceholderQR($bookingReference);
            }
        } catch (Exception $e) {
            error_log("QR Code generation failed: " . $e->getMessage());
            return $this->generatePlaceholderQR($bookingReference);
        }
    }

    /**
     * Generate a placeholder QR code when external service fails
     */
    private function generatePlaceholderQR($bookingReference) {
        // Create a simple SVG QR code placeholder
        $svg = '<?xml version="1.0" encoding="UTF-8"?>
        <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="white" stroke="black" stroke-width="2"/>
            <rect x="20" y="20" width="20" height="20" fill="black"/>
            <rect x="60" y="20" width="20" height="20" fill="black"/>
            <rect x="100" y="20" width="20" height="20" fill="black"/>
            <rect x="140" y="20" width="20" height="20" fill="black"/>
            <rect x="20" y="60" width="20" height="20" fill="black"/>
            <rect x="140" y="60" width="20" height="20" fill="black"/>
            <rect x="20" y="100" width="20" height="20" fill="black"/>
            <rect x="60" y="100" width="20" height="20" fill="black"/>
            <rect x="100" y="100" width="20" height="20" fill="black"/>
            <rect x="140" y="100" width="20" height="20" fill="black"/>
            <rect x="20" y="140" width="20" height="20" fill="black"/>
            <rect x="60" y="140" width="20" height="20" fill="black"/>
            <rect x="100" y="140" width="20" height="20" fill="black"/>
            <rect x="140" y="140" width="20" height="20" fill="black"/>
            <text x="100" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="black">QR: ' . htmlspecialchars($bookingReference) . '</text>
        </svg>';

        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }
    
    /**
     * Generate HTML ticket for a booking (simplified version without PDF library)
     */
    public function generateHTMLTicket($bookingReference) {
        try {
            // Get booking details
            $booking = $this->getBookingDetails($bookingReference);
            if (!$booking) {
                throw new Exception("Booking not found");
            }

            // Generate QR code
            $qrCodeDataUri = $this->generateQRCode($bookingReference, 200);

            // Build ticket HTML
            $html = $this->buildTicketHTML($booking, $qrCodeDataUri);

            return $html;

        } catch (Exception $e) {
            error_log("HTML ticket generation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate PDF ticket for a booking (simplified version)
     */
    public function generatePDFTicket($bookingReference) {
        // For now, return HTML content that can be printed as PDF by browser
        return $this->generateHTMLTicket($bookingReference);
    }
    
    /**
     * Get booking details with event information
     */
    private function getBookingDetails($bookingReference) {
        $this->db->query('SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location, 
                         e.organizer, e.organizer_contact, e.image_url, e.description
                         FROM bookings b
                         JOIN events e ON b.event_id = e.id
                         WHERE b.booking_reference = :reference');
        $this->db->bind(':reference', $bookingReference);
        return $this->db->single();
    }
    
    /**
     * Build HTML content for PDF ticket
     */
    private function buildTicketHTML($booking, $qrCodeDataUri) {
        $eventDate = formatDate($booking->event_date);
        $eventTime = formatTime($booking->event_time);
        $totalAmount = formatCurrency($booking->total_amount);
        
        $html = '
        <style>
            .ticket-container { border: 2px solid #007bff; border-radius: 10px; padding: 20px; margin: 10px 0; }
            .ticket-header { text-align: center; background-color: #007bff; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
            .ticket-title { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
            .ticket-subtitle { font-size: 14px; opacity: 0.9; }
            .event-details { margin: 20px 0; }
            .detail-row { margin: 8px 0; }
            .detail-label { font-weight: bold; color: #333; }
            .detail-value { color: #666; }
            .qr-section { text-align: center; margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }
            .booking-ref { font-size: 18px; font-weight: bold; color: #007bff; text-align: center; margin: 15px 0; }
            .footer-info { font-size: 10px; color: #666; text-align: center; margin-top: 20px; border-top: 1px solid #ddd; padding-top: 10px; }
        </style>
        
        <div class="ticket-container">
            <div class="ticket-header">
                <div class="ticket-title">' . htmlspecialchars($booking->title) . '</div>
                <div class="ticket-subtitle">Event Ticket</div>
            </div>
            
            <div class="booking-ref">Booking Reference: ' . htmlspecialchars($booking->booking_reference) . '</div>
            
            <div class="event-details">
                <div class="detail-row">
                    <span class="detail-label">Attendee:</span>
                    <span class="detail-value">' . htmlspecialchars($booking->attendee_name) . '</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value">' . htmlspecialchars($booking->attendee_email) . '</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date & Time:</span>
                    <span class="detail-value">' . $eventDate . ' at ' . $eventTime . '</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Venue:</span>
                    <span class="detail-value">' . htmlspecialchars($booking->venue) . '</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Location:</span>
                    <span class="detail-value">' . htmlspecialchars($booking->location) . '</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Tickets:</span>
                    <span class="detail-value">' . $booking->quantity . ' ticket(s)</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Total Amount:</span>
                    <span class="detail-value">' . $totalAmount . '</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Organizer:</span>
                    <span class="detail-value">' . htmlspecialchars($booking->organizer) . '</span>
                </div>
            </div>
            
            <div class="qr-section">
                <div style="margin-bottom: 10px;"><strong>Scan QR Code for Entry</strong></div>';
        
        if ($qrCodeDataUri) {
            $html .= '<img src="' . $qrCodeDataUri . '" style="width: 150px; height: 150px;" />';
        } else {
            $html .= '<div style="width: 150px; height: 150px; border: 2px dashed #ccc; display: inline-block; line-height: 150px;">QR Code</div>';
        }
        
        $html .= '
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    Present this QR code at the event entrance
                </div>
            </div>
            
            <div class="footer-info">
                <div><strong>Important:</strong> Please arrive 30 minutes before the event starts</div>
                <div>Bring a valid ID that matches the attendee name</div>
                <div>This ticket is non-transferable and non-refundable</div>
                <div style="margin-top: 10px;">Generated on ' . date('F j, Y \a\t g:i A') . '</div>
                <div>' . SITE_NAME . ' - ' . SITE_URL . '</div>
            </div>
        </div>';
        
        return $html;
    }
    
    /**
     * Download ticket as HTML (printable)
     */
    public function downloadTicket($bookingReference, $filename = null) {
        $htmlContent = $this->generateHTMLTicket($bookingReference);

        if (!$htmlContent) {
            return false;
        }

        if (!$filename) {
            $filename = 'ticket_' . $bookingReference . '.html';
        }

        // Get booking details for title
        $booking = $this->getBookingDetails($bookingReference);
        $pageTitle = $booking ? $booking->title . ' - Ticket' : 'Event Ticket';

        // Create complete HTML document
        $fullHtml = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($pageTitle) . '</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .card { border: 2px solid #000 !important; box-shadow: none !important; }
            body { background: white !important; }
        }
        .ticket-container { max-width: 800px; margin: 20px auto; }
    </style>
</head>
<body>
    <div class="container ticket-container">
        <div class="no-print mb-3 text-center">
            <button onclick="window.print()" class="btn btn-primary me-2">
                <i class="fas fa-print me-2"></i>Print Ticket
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                <i class="fas fa-times me-2"></i>Close
            </button>
        </div>
        ' . $htmlContent . '
    </div>
    <script>
        // Auto-print when page loads
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>';

        // Set headers for HTML download
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: inline; filename="' . $filename . '"');
        header('Cache-Control: private, max-age=0, must-revalidate');

        echo $fullHtml;
        return true;
    }
}
?>
