<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Get event ID from URL
$eventId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$eventId) {
    setFlashMessage('danger', 'Event not found.');
    redirect('../index.php');
}

// Get event details
$event = $eventManager->getEventById($eventId);

if (!$event) {
    setFlashMessage('danger', 'Event not found.');
    redirect('../index.php');
}

$pageTitle = $event->title;
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Event Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="search.php">Events</a></li>
                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($event->title); ?></li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Event Image and Details -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <img src="<?php echo $event->image_url ?: '../assets/images/default-event.jpg'; ?>"
                     class="card-img-top" alt="<?php echo htmlspecialchars($event->title); ?>"
                     style="height: 400px; object-fit: cover;">

                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h1 class="card-title h2"><?php echo htmlspecialchars($event->title); ?></h1>
                        <span class="badge bg-primary fs-6"><?php echo formatCurrency($event->price); ?></span>
                    </div>

                    <!-- Event Meta Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-calendar text-primary me-2"></i>
                                <strong>Date:</strong>
                                <span class="ms-2"><?php echo formatDate($event->event_date); ?></span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-clock text-primary me-2"></i>
                                <strong>Time:</strong>
                                <span class="ms-2"><?php echo formatTime($event->event_time); ?></span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <strong>Venue:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($event->venue); ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-building text-primary me-2"></i>
                                <strong>Organizer:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($event->organizer); ?></span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <strong>Contact:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($event->organizer_contact); ?></span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-ticket-alt text-primary me-2"></i>
                                <strong>Available:</strong>
                                <span class="ms-2"><?php echo $event->available_tickets; ?> / <?php echo $event->total_tickets; ?> tickets</span>
                            </div>
                        </div>
                    </div>

                    <!-- Event Description -->
                    <div class="mb-4">
                        <h3>About This Event</h3>
                        <p class="text-muted"><?php echo nl2br(htmlspecialchars($event->description)); ?></p>
                    </div>

                    <!-- Location Map Placeholder -->
                    <div class="mb-4">
                        <h3>Location</h3>
                        <div class="bg-light p-4 rounded text-center">
                            <i class="fas fa-map text-muted" style="font-size: 48px;"></i>
                            <p class="mt-2 mb-0 text-muted">
                                <strong><?php echo htmlspecialchars($event->venue); ?></strong><br>
                                <?php echo htmlspecialchars($event->location); ?>
                            </p>
                            <small class="text-muted">Interactive map would be integrated here</small>
                        </div>
                    </div>
                    
                    
                </div>
                
            </div>
        </div>

        <!-- Booking Sidebar -->
        <div class="col-lg-4">
            <div class="card shadow sticky-top" style="top: 20px;">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>Book Tickets</h4>
                </div>
                <div class="card-body">
                    <?php if ($event->available_tickets > 0): ?>
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Number of Tickets</label>
                            <select class="form-select" id="quantity">
                                <?php for ($i = 1; $i <= min(10, $event->available_tickets); $i++): ?>
                                    <option value="<?php echo $i; ?>"><?php echo $i; ?> ticket<?php echo $i > 1 ? 's' : ''; ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Price per ticket:</span>
                                <strong><?php echo formatCurrency($event->price); ?></strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Total:</span>
                                <strong id="totalPrice"><?php echo formatCurrency($event->price); ?></strong>
                            </div>
                        </div>

                        <button class="btn btn-primary w-100 mb-3" onclick="addToCartWithQuantity()">
                            <i class="fas fa-cart-plus me-2"></i>Add to Cart
                        </button>

                        <?php if (isLoggedIn()): ?>
                            <a href="../booking/checkout.php?event_id=<?php echo $event->id; ?>&quantity=1"
                               class="btn btn-success w-100" id="buyNowBtn">
                                <i class="fas fa-bolt me-2"></i>Buy Now
                            </a>
                        <?php else: ?>
                            <a href="../auth/login.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>"
                               class="btn btn-success w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Login to Book
                            </a>
                        <?php endif; ?>

                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>Secure booking guaranteed
                            </small>
                        </div>
                    <?php else: ?>
                        <div class="text-center">
                            <i class="fas fa-times-circle text-danger" style="font-size: 48px;"></i>
                            <h5 class="mt-3 text-danger">Sold Out</h5>
                            <p class="text-muted">This event is currently sold out. Check back later for availability.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Event Info -->
            <div class="card shadow mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Event Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Category:</strong>
                        <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($event->category); ?></span>
                    </div>
                    <div class="mb-2">
                        <strong>Status:</strong>
                        <span class="badge bg-success ms-2"><?php echo ucfirst($event->status); ?></span>
                    </div>
                    <div class="mb-2">
                        <strong>Event ID:</strong>
                        <span class="text-muted ms-2">#<?php echo $event->id; ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update total price when quantity changes
document.getElementById('quantity').addEventListener('change', function() {
    const quantity = parseInt(this.value);
    const pricePerTicket = <?php echo $event->price; ?>;
    const total = quantity * pricePerTicket;

    document.getElementById('totalPrice').textContent = Math.round(total) + ' XAF';

    // Update buy now link
    const buyNowBtn = document.getElementById('buyNowBtn');
    if (buyNowBtn) {
        const baseUrl = buyNowBtn.href.split('&quantity=')[0];
        buyNowBtn.href = baseUrl + '&quantity=' + quantity;
    }
});

function addToCartWithQuantity() {
    const quantity = parseInt(document.getElementById('quantity').value);
    addToCart(<?php echo $event->id; ?>, quantity);
}

// Share functionality
function shareEvent() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($event->title); ?>',
            text: 'Check out this amazing event!',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(function() {
            showAlert('success', 'Event link copied to clipboard!');
        });
    }
}
</script>

<?php include '../includes/footer.php'; ?>
